/* Product Detail Page Styles */

/* Breadcrumb */
.breadcrumb-container {
    background-color: #f8f9fa;
    padding: 1rem 0;
}

.breadcrumb {
    display: flex;
    gap: 0.5rem;
    color: #666;
}

.breadcrumb a {
    color: var(--accent-color);
    transition: var(--transition);
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb .product-name {
    color: var(--primary-color);
    font-weight: 500;
}

/* Product Detail Section */
.product-detail-section {
    padding: 3rem 0;
}

.product-detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

/* Product Images */
.product-images {
    position: sticky;
    top: 100px;
}

.main-image {
    position: relative;
    margin-bottom: 1rem;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.main-image img {
    width: 100%;
    height: auto;
    display: block;
}

.product-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-tag.new {
    background-color: var(--accent-color);
    color: white;
}

.thumbnail-images {
    display: flex;
    gap: 0.5rem;
}

.thumbnail-images img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: var(--transition);
}

.thumbnail-images img:hover,
.thumbnail-images img.active {
    border-color: var(--secondary-color);
}

/* Product Info */
.product-info {
    padding: 1rem 0;
}

.product-name {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.stars {
    color: #FFD700;
}

.product-description {
    margin-bottom: 2rem;
    line-height: 1.8;
    color: #555;
}

.product-colors,
.product-sizes {
    margin-bottom: 1.5rem;
}

.product-colors h3,
.product-sizes h3,
.product-quantity h3 {
    font-size: 1rem;
    margin-bottom: 0.8rem;
    color: var(--primary-color);
}

.color-options {
    display: flex;
    gap: 0.8rem;
}

.color-option {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid transparent;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.color-option.active,
.color-option:hover {
    border-color: var(--primary-color);
}

.color-option.active::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

.size-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
}

.size-option {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.size-option.active,
.size-option:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--primary-color);
}

.quantity-selector {
    display: flex;
    align-items: center;
    max-width: 150px;
    margin-bottom: 2rem;
}

.quantity-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    cursor: pointer;
    transition: var(--transition);
}

.quantity-btn.minus {
    border-radius: 4px 0 0 4px;
}

.quantity-btn.plus {
    border-radius: 0 4px 4px 0;
}

.quantity-btn:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--primary-color);
}

.quantity-input {
    width: 70px;
    height: 40px;
    border: 1px solid #ddd;
    border-left: none;
    border-right: none;
    text-align: center;
    font-size: 1rem;
}

.quantity-input::-webkit-inner-spin-button,
.quantity-input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.product-actions {
    display: flex;
    gap: 1rem;
}

.product-actions .btn {
    flex: 1;
    padding: 0.8rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.wishlist-btn i {
    transition: var(--transition);
}

.wishlist-btn:hover i {
    color: var(--accent-color);
}

/* Product Tabs */
.product-tabs-section {
    padding: 3rem 0;
    background-color: #f8f9fa;
}

.product-tabs {
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.tabs-header {
    display: flex;
    border-bottom: 1px solid #eee;
}

.tab-btn {
    padding: 1rem 2rem;
    background: none;
    border: none;
    font-size: 1rem;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.tab-btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

.tab-btn:hover,
.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn.active::after {
    width: 100%;
}

.tabs-content {
    padding: 2rem;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.tab-panel h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.tab-panel p {
    margin-bottom: 1rem;
    line-height: 1.8;
    color: #555;
}

.tab-panel ul {
    list-style: disc;
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.tab-panel ul li {
    margin-bottom: 0.5rem;
    color: #555;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
}

.details-table th,
.details-table td {
    padding: 0.8rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.details-table th {
    width: 30%;
    color: var(--primary-color);
    font-weight: 500;
}

.details-table td {
    color: #555;
}

/* Reviews */
.reviews-summary {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.average-rating {
    text-align: center;
}

.rating-number {
    font-size: 3rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.total-reviews {
    color: #666;
    font-size: 0.9rem;
}

.rating-bars {
    flex: 1;
}

.rating-bar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.rating-label {
    width: 60px;
    color: #666;
    font-size: 0.9rem;
}

.bar-container {
    flex: 1;
    height: 8px;
    background-color: #eee;
    border-radius: 4px;
    overflow: hidden;
}

.bar {
    height: 100%;
    background-color: var(--secondary-color);
}

.rating-count {
    width: 30px;
    text-align: right;
    color: #666;
    font-size: 0.9rem;
}

.reviews-list {
    margin-bottom: 2rem;
}

.review-item {
    padding: 1.5rem 0;
    border-bottom: 1px solid #eee;
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.8rem;
}

.reviewer-name {
    font-weight: 500;
    color: var(--primary-color);
}

.review-date {
    color: #666;
    font-size: 0.9rem;
}

.review-rating {
    color: #FFD700;
}

.review-content p {
    line-height: 1.6;
    color: #555;
}

.write-review {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.write-review h4 {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.review-form .form-group {
    margin-bottom: 1.5rem;
}

.review-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.rating-input {
    color: #ddd;
    font-size: 1.5rem;
    cursor: pointer;
}

.rating-input i {
    margin-right: 0.3rem;
}

.rating-input .fas {
    color: #FFD700;
}

.review-form textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    resize: vertical;
}

.review-form textarea:focus {
    border-color: var(--secondary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(248, 194, 145, 0.2);
}

/* Related Products */
.related-products {
    padding: 3rem 0;
}

/* Responsive Design */
@media (max-width: 992px) {
    .product-detail-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .product-images {
        position: static;
    }
    
    .reviews-summary {
        flex-direction: column;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .tabs-header {
        flex-direction: column;
    }
    
    .tab-btn {
        width: 100%;
        text-align: left;
        padding: 1rem;
    }
    
    .tabs-content {
        padding: 1.5rem;
    }
    
    .product-actions {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .product-name {
        font-size: 1.5rem;
    }
    
    .product-price {
        font-size: 1.5rem;
    }
}
