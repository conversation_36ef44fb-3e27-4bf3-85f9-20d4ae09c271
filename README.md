# 5RRR Store - Premium Clothing & Lifestyle E-commerce Website

A modern, responsive e-commerce website for clothing, jewelry, perfumes, and other lifestyle essentials.

## Features

- **Modern Design**: Clean, responsive interface with hybrid standard styling
- **Authentication**: User login and signup functionality with Supabase integration
- **Product Catalog**: Browse products by category with filtering and search
- **Shopping Cart**: Add products to cart with local storage
- **Product Details**: Detailed product pages with reviews and ratings
- **Mobile Responsive**: Optimized for all device sizes

## Technologies Used

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Supabase (for authentication and database)
- **Styling**: Custom CSS with CSS Variables
- **Icons**: Font Awesome
- **Development Server**: Node.js HTTP server

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Installation & Running

#### Option 1: Using Node.js Server (Recommended)

1. **Install Node.js dependencies** (if Node.js is available):
   ```bash
   npm install
   ```

2. **Start the development server**:
   ```bash
   npm run dev
   ```
   Or manually:
   ```bash
   node server.js
   ```

3. **Open your browser** and navigate to:
   ```
   http://localhost:3308
   ```

#### Option 2: Using HTTP Server (Alternative)

1. **Install http-server globally**:
   ```bash
   npm install -g http-server
   ```

2. **Start the server**:
   ```bash
   http-server . -p 3308 -o
   ```

#### Option 3: Using VS Code (No PHP Required)

1. **Open the project in VS Code**
2. **Press F5** or go to Run > Start Debugging
3. **Select "Launch 5RRR Store"** from the configuration dropdown
4. The server will start automatically and open in Chrome

#### Option 4: Simple File Server

If you don't have Node.js installed, you can use any simple HTTP server:

- **Python 3**: `python -m http.server 3308`
- **Python 2**: `python -m SimpleHTTPServer 3308`
- **PHP**: `php -S localhost:3308` (if PHP is available)

## Project Structure

```
5RRR Store/
├── index.html              # Homepage
├── login.html              # Login page
├── signup.html             # Signup page
├── products.html           # Products listing page
├── product-detail.html     # Product detail page
├── styles/                 # CSS files
│   ├── main.css           # Main styles
│   ├── auth.css           # Authentication styles
│   ├── products.css       # Products page styles
│   └── product-detail.css # Product detail styles
├── js/                     # JavaScript files
│   ├── main.js            # Main functionality
│   ├── auth.js            # Authentication logic
│   ├── products.js        # Products functionality
│   └── supabase.js        # Supabase configuration
├── .vscode/               # VS Code configuration
│   ├── launch.json        # Debug configuration
│   └── tasks.json         # Task configuration
├── server.js              # Node.js development server
├── package.json           # Node.js dependencies
└── README.md              # This file
```

## Configuration

### Supabase Setup

1. **Create a Supabase project** at [supabase.com](https://supabase.com)
2. **Get your project URL and anon key** from Project Settings > API
3. **Update `js/supabase.js`** with your credentials:
   ```javascript
   const supabaseUrl = 'YOUR_SUPABASE_URL'
   const supabaseKey = 'YOUR_SUPABASE_ANON_KEY'
   ```

### Database Schema

The project includes a complete database schema in `js/supabase.js`. Create these tables in your Supabase project:

- `profiles` - User profiles
- `products` - Product information
- `product_variants` - Product variants (colors, sizes)
- `orders` - Customer orders
- `order_items` - Items in orders
- `reviews` - Product reviews

## Development

### Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with auto-reload
- `npm run serve` - Start server without opening browser

### VS Code Tasks

- **Start 5RRR Store Server** - Launch the Node.js server
- **Start HTTP Server (Alternative)** - Use http-server package
- **Install Dependencies** - Install npm packages

### Debugging

Use VS Code's built-in debugger:
1. Set breakpoints in your JavaScript files
2. Press F5 to start debugging
3. Choose "Launch 5RRR Store" configuration

## Troubleshooting

### "PHP not found" Error

This project doesn't require PHP. If you're seeing PHP errors:

1. **Use the Node.js server** instead: `node server.js`
2. **Use VS Code tasks** to start the server
3. **Install http-server**: `npm install -g http-server`

### Port Already in Use

If port 3308 is busy:
1. **Change the port** in `server.js` and `.vscode/launch.json`
2. **Kill existing processes**: `lsof -ti:3308 | xargs kill -9` (macOS/Linux)

### Module Import Errors

If you see ES6 import errors:
1. **Serve files through HTTP server** (not file:// protocol)
2. **Use the provided server configurations**

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions, please contact: <EMAIL>
