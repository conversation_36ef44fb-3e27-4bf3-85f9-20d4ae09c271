// Products functionality for 5RRR Store
import supabase from './supabase.js';

// Sample product data (will be replaced with Supabase data)
const sampleProducts = [
    {
        id: 1,
        name: 'Premium Cotton T-Shirt',
        price: 49.99,
        category: 'clothing',
        image: 'images/products/tshirt-1.jpg',
        description: 'Luxurious cotton t-shirt with a comfortable fit and stylish design.',
        featured: true,
        new: true,
        colors: ['Black', 'White', 'Navy'],
        sizes: ['S', 'M', 'L', 'XL']
    },
    {
        id: 2,
        name: 'Designer Jeans',
        price: 89.99,
        category: 'clothing',
        image: 'images/products/jeans-1.jpg',
        description: 'High-quality designer jeans with perfect fit and durability.',
        featured: true,
        new: false,
        colors: ['Blue', 'Black'],
        sizes: ['28', '30', '32', '34', '36']
    },
    {
        id: 3,
        name: 'Gold Pendant Necklace',
        price: 129.99,
        category: 'jewelry',
        image: 'images/products/necklace-1.jpg',
        description: 'Elegant gold pendant necklace, perfect for any occasion.',
        featured: true,
        new: true,
        colors: ['Gold', 'Silver'],
        sizes: []
    },
    {
        id: 4,
        name: 'Luxury Perfume',
        price: 79.99,
        category: 'perfumes',
        image: 'images/products/perfume-1.jpg',
        description: 'Exquisite fragrance with notes of jasmine, vanilla, and sandalwood.',
        featured: true,
        new: false,
        colors: [],
        sizes: []
    },
    {
        id: 5,
        name: 'Leather Wallet',
        price: 59.99,
        category: 'essentials',
        image: 'images/products/wallet-1.jpg',
        description: 'Genuine leather wallet with multiple card slots and sleek design.',
        featured: true,
        new: false,
        colors: ['Brown', 'Black'],
        sizes: []
    },
    {
        id: 6,
        name: 'Silk Scarf',
        price: 39.99,
        category: 'essentials',
        image: 'images/products/scarf-1.jpg',
        description: 'Luxurious silk scarf with beautiful pattern and vibrant colors.',
        featured: true,
        new: true,
        colors: ['Multicolor', 'Blue', 'Red'],
        sizes: []
    }
];

// DOM Elements
document.addEventListener('DOMContentLoaded', () => {
    // Load featured products on home page
    const featuredProductsGrid = document.querySelector('.featured-products .product-grid');
    if (featuredProductsGrid) {
        loadFeaturedProducts(featuredProductsGrid);
    }

    // Load products on products page
    const productsGrid = document.querySelector('.products-page .product-grid');
    if (productsGrid) {
        loadAllProducts(productsGrid);
        setupFilters();
    }

    // Load single product on product detail page
    const productDetail = document.querySelector('.product-detail');
    if (productDetail) {
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('id');
        if (productId) {
            loadProductDetail(productId, productDetail);
        }
    }
});

/**
 * Load featured products on home page
 * @param {HTMLElement} container - Container for products
 */
async function loadFeaturedProducts(container) {
    try {
        // In the future, this will fetch from Supabase
        // const { data, error } = await supabase
        //     .from('products')
        //     .select('*')
        //     .eq('featured', true)
        //     .limit(6);
        
        // if (error) throw error;
        
        // For now, use sample data
        const featuredProducts = sampleProducts.filter(product => product.featured);
        
        renderProducts(featuredProducts, container);
    } catch (error) {
        console.error('Error loading featured products:', error);
        container.innerHTML = '<p class="error-message">Failed to load products. Please try again later.</p>';
    }
}

/**
 * Load all products with optional filtering
 * @param {HTMLElement} container - Container for products
 * @param {Object} filters - Filter options
 */
async function loadAllProducts(container, filters = {}) {
    try {
        // Show loading state
        container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading products...</div>';
        
        // In the future, this will fetch from Supabase with filters
        // let query = supabase.from('products').select('*');
        
        // if (filters.category) {
        //     query = query.eq('category', filters.category);
        // }
        
        // if (filters.minPrice) {
        //     query = query.gte('price', filters.minPrice);
        // }
        
        // if (filters.maxPrice) {
        //     query = query.lte('price', filters.maxPrice);
        // }
        
        // const { data, error } = await query;
        
        // if (error) throw error;
        
        // For now, use sample data with filtering
        let filteredProducts = [...sampleProducts];
        
        if (filters.category) {
            filteredProducts = filteredProducts.filter(product => product.category === filters.category);
        }
        
        if (filters.minPrice) {
            filteredProducts = filteredProducts.filter(product => product.price >= filters.minPrice);
        }
        
        if (filters.maxPrice) {
            filteredProducts = filteredProducts.filter(product => product.price <= filters.maxPrice);
        }
        
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filteredProducts = filteredProducts.filter(product => 
                product.name.toLowerCase().includes(searchTerm) || 
                product.description.toLowerCase().includes(searchTerm)
            );
        }
        
        // Clear loading state
        container.innerHTML = '';
        
        if (filteredProducts.length === 0) {
            container.innerHTML = '<p class="no-products">No products found matching your criteria.</p>';
            return;
        }
        
        renderProducts(filteredProducts, container);
    } catch (error) {
        console.error('Error loading products:', error);
        container.innerHTML = '<p class="error-message">Failed to load products. Please try again later.</p>';
    }
}

/**
 * Render products to container
 * @param {Array} products - Array of product objects
 * @param {HTMLElement} container - Container for products
 */
function renderProducts(products, container) {
    products.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        
        productCard.innerHTML = `
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}">
                ${product.new ? '<div class="product-tag new">New</div>' : ''}
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-category">${capitalizeFirstLetter(product.category)}</p>
                <p class="product-price">$${product.price.toFixed(2)}</p>
                <div class="product-actions">
                    <a href="product-detail.html?id=${product.id}" class="btn btn-outline">View Details</a>
                    <button class="btn btn-primary add-to-cart" data-product-id="${product.id}">
                        <i class="fas fa-shopping-cart"></i> Add to Cart
                    </button>
                </div>
            </div>
        `;
        
        container.appendChild(productCard);
    });
    
    // Add event listeners to add-to-cart buttons
    const addToCartButtons = container.querySelectorAll('.add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', handleAddToCart);
    });
}

/**
 * Load product detail
 * @param {string|number} productId - Product ID
 * @param {HTMLElement} container - Container for product detail
 */
async function loadProductDetail(productId, container) {
    try {
        // Show loading state
        container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading product details...</div>';
        
        // In the future, this will fetch from Supabase
        // const { data, error } = await supabase
        //     .from('products')
        //     .select('*')
        //     .eq('id', productId)
        //     .single();
        
        // if (error) throw error;
        
        // For now, use sample data
        const product = sampleProducts.find(p => p.id === parseInt(productId));
        
        if (!product) {
            container.innerHTML = '<p class="error-message">Product not found.</p>';
            return;
        }
        
        renderProductDetail(product, container);
    } catch (error) {
        console.error('Error loading product detail:', error);
        container.innerHTML = '<p class="error-message">Failed to load product details. Please try again later.</p>';
    }
}

/**
 * Render product detail
 * @param {Object} product - Product object
 * @param {HTMLElement} container - Container for product detail
 */
function renderProductDetail(product, container) {
    container.innerHTML = `
        <div class="product-detail-grid">
            <div class="product-images">
                <div class="main-image">
                    <img src="${product.image}" alt="${product.name}">
                    ${product.new ? '<div class="product-tag new">New</div>' : ''}
                </div>
                <div class="thumbnail-images">
                    <img src="${product.image}" alt="${product.name}" class="active">
                    <!-- Additional images would be added here -->
                </div>
            </div>
            <div class="product-info">
                <h1 class="product-name">${product.name}</h1>
                <p class="product-price">$${product.price.toFixed(2)}</p>
                <div class="product-rating">
                    <div class="stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                    <span>(4.5/5 - 24 reviews)</span>
                </div>
                <p class="product-description">${product.description}</p>
                
                ${product.colors.length > 0 ? `
                <div class="product-colors">
                    <h3>Color:</h3>
                    <div class="color-options">
                        ${product.colors.map(color => `
                            <button class="color-option" data-color="${color.toLowerCase()}" style="background-color: ${color.toLowerCase()};">
                                <span class="sr-only">${color}</span>
                            </button>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
                
                ${product.sizes.length > 0 ? `
                <div class="product-sizes">
                    <h3>Size:</h3>
                    <div class="size-options">
                        ${product.sizes.map(size => `
                            <button class="size-option" data-size="${size}">${size}</button>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
                
                <div class="product-quantity">
                    <h3>Quantity:</h3>
                    <div class="quantity-selector">
                        <button class="quantity-btn minus"><i class="fas fa-minus"></i></button>
                        <input type="number" value="1" min="1" max="10" class="quantity-input">
                        <button class="quantity-btn plus"><i class="fas fa-plus"></i></button>
                    </div>
                </div>
                
                <div class="product-actions">
                    <button class="btn btn-primary add-to-cart" data-product-id="${product.id}">
                        <i class="fas fa-shopping-cart"></i> Add to Cart
                    </button>
                    <button class="btn btn-outline wishlist-btn" data-product-id="${product.id}">
                        <i class="far fa-heart"></i> Add to Wishlist
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Add event listeners
    setupProductDetailEvents(container, product);
}

/**
 * Setup event listeners for product detail page
 * @param {HTMLElement} container - Product detail container
 * @param {Object} product - Product object
 */
function setupProductDetailEvents(container, product) {
    // Color selection
    const colorOptions = container.querySelectorAll('.color-option');
    colorOptions.forEach(option => {
        option.addEventListener('click', () => {
            colorOptions.forEach(opt => opt.classList.remove('active'));
            option.classList.add('active');
        });
    });
    
    // Size selection
    const sizeOptions = container.querySelectorAll('.size-option');
    sizeOptions.forEach(option => {
        option.addEventListener('click', () => {
            sizeOptions.forEach(opt => opt.classList.remove('active'));
            option.classList.add('active');
        });
    });
    
    // Quantity buttons
    const minusBtn = container.querySelector('.quantity-btn.minus');
    const plusBtn = container.querySelector('.quantity-btn.plus');
    const quantityInput = container.querySelector('.quantity-input');
    
    minusBtn.addEventListener('click', () => {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
        }
    });
    
    plusBtn.addEventListener('click', () => {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue < 10) {
            quantityInput.value = currentValue + 1;
        }
    });
    
    // Add to cart button
    const addToCartBtn = container.querySelector('.add-to-cart');
    addToCartBtn.addEventListener('click', () => {
        const selectedColor = container.querySelector('.color-option.active')?.dataset.color;
        const selectedSize = container.querySelector('.size-option.active')?.dataset.size;
        const quantity = parseInt(quantityInput.value);
        
        handleAddToCart(null, {
            productId: product.id,
            color: selectedColor,
            size: selectedSize,
            quantity: quantity
        });
    });
    
    // Wishlist button
    const wishlistBtn = container.querySelector('.wishlist-btn');
    wishlistBtn.addEventListener('click', () => {
        toggleWishlist(product.id);
    });
}

/**
 * Handle adding product to cart
 * @param {Event} e - Click event (optional)
 * @param {Object} options - Product options (optional)
 */
function handleAddToCart(e, options = null) {
    let productId, color, size, quantity;
    
    if (e) {
        e.preventDefault();
        productId = e.currentTarget.dataset.productId;
        color = null;
        size = null;
        quantity = 1;
    } else {
        productId = options.productId;
        color = options.color;
        size = options.size;
        quantity = options.quantity || 1;
    }
    
    // Get cart from localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Check if product already in cart
    const existingProductIndex = cart.findIndex(item => 
        item.productId === productId && 
        item.color === color && 
        item.size === size
    );
    
    if (existingProductIndex !== -1) {
        // Update quantity if product already in cart
        cart[existingProductIndex].quantity += quantity;
    } else {
        // Add new product to cart
        cart.push({
            productId,
            color,
            size,
            quantity
        });
    }
    
    // Save cart to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));
    
    // Show success message
    showMessage('Product added to cart!', 'success');
    
    // Update cart count
    updateCartCount();
}

/**
 * Toggle product in wishlist
 * @param {string|number} productId - Product ID
 */
function toggleWishlist(productId) {
    // Get wishlist from localStorage
    let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
    
    // Check if product already in wishlist
    const existingIndex = wishlist.indexOf(productId);
    
    if (existingIndex !== -1) {
        // Remove from wishlist
        wishlist.splice(existingIndex, 1);
        showMessage('Product removed from wishlist!', 'success');
    } else {
        // Add to wishlist
        wishlist.push(productId);
        showMessage('Product added to wishlist!', 'success');
    }
    
    // Save wishlist to localStorage
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
}

/**
 * Update cart count in header
 */
function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const cartCount = cart.reduce((total, item) => total + item.quantity, 0);
    
    const cartIcon = document.querySelector('.cart-icon');
    
    if (cartIcon) {
        // Check if count badge exists
        let countBadge = cartIcon.querySelector('.cart-count');
        
        if (!countBadge) {
            // Create count badge
            countBadge = document.createElement('span');
            countBadge.className = 'cart-count';
            cartIcon.appendChild(countBadge);
        }
        
        // Update count
        countBadge.textContent = cartCount;
        
        // Show/hide badge
        if (cartCount > 0) {
            countBadge.style.display = 'flex';
        } else {
            countBadge.style.display = 'none';
        }
    }
}

/**
 * Setup filter functionality on products page
 */
function setupFilters() {
    const filterForm = document.querySelector('.filter-form');
    
    if (!filterForm) return;
    
    filterForm.addEventListener('submit', e => {
        e.preventDefault();
        
        const category = document.querySelector('#category-filter').value;
        const minPrice = document.querySelector('#min-price').value;
        const maxPrice = document.querySelector('#max-price').value;
        const search = document.querySelector('#search-input').value;
        
        const filters = {
            category: category !== 'all' ? category : null,
            minPrice: minPrice ? parseFloat(minPrice) : null,
            maxPrice: maxPrice ? parseFloat(maxPrice) : null,
            search: search || null
        };
        
        const productsGrid = document.querySelector('.products-page .product-grid');
        loadAllProducts(productsGrid, filters);
    });
    
    // Reset filters
    const resetBtn = document.querySelector('.reset-filters');
    if (resetBtn) {
        resetBtn.addEventListener('click', () => {
            filterForm.reset();
            const productsGrid = document.querySelector('.products-page .product-grid');
            loadAllProducts(productsGrid);
        });
    }
}

/**
 * Show message to user
 * @param {string} message - Message to display
 * @param {string} type - Message type (success, error)
 */
function showMessage(message, type) {
    // Check if message container exists, if not create it
    let messageContainer = document.querySelector('.message-container');
    
    if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.className = 'message-container';
        document.body.appendChild(messageContainer);
    }
    
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    messageElement.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add to container
    messageContainer.appendChild(messageElement);
    
    // Remove after delay
    setTimeout(() => {
        messageElement.classList.add('fade-out');
        setTimeout(() => {
            messageElement.remove();
        }, 500);
    }, 3000);
}

/**
 * Capitalize first letter of string
 * @param {string} string - String to capitalize
 * @returns {string} Capitalized string
 */
function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

// Initialize cart count on page load
updateCartCount();
