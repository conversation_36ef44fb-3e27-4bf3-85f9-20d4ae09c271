/* Products Page Styles */

/* <PERSON> Header */
.page-header {
    background-color: #f8f9fa;
    padding: 3rem 0;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    color: #666;
}

.breadcrumb a {
    color: var(--accent-color);
    transition: var(--transition);
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Products Layout */
.products-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 2rem;
    margin: 3rem 0;
}

/* Sidebar */
.sidebar {
    position: sticky;
    top: 100px;
    height: fit-content;
}

.filter-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-section h3 {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 0.5rem;
}

.filter-section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--secondary-color);
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.filter-group select,
.filter-group input[type="text"],
.filter-group input[type="number"] {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: var(--transition);
}

.filter-group select:focus,
.filter-group input:focus {
    border-color: var(--secondary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(248, 194, 145, 0.2);
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.price-inputs input {
    flex: 1;
}

.price-inputs span {
    color: #666;
}

.search-input-wrapper {
    position: relative;
}

.search-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    color: var(--accent-color);
}

.filter-actions {
    display: flex;
    gap: 1rem;
}

.filter-actions button {
    flex: 1;
    padding: 0.8rem;
    font-size: 0.9rem;
}

.promo-card {
    background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
}

.promo-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.promo-card p {
    margin-bottom: 1rem;
    opacity: 0.9;
}

.btn-accent {
    background-color: white;
    color: var(--accent-color);
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    display: inline-block;
    transition: var(--transition);
}

.btn-accent:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

/* Products Container */
.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.products-count {
    color: #666;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-options label {
    color: #666;
}

.sort-options select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

/* Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.product-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-tag.new {
    background-color: var(--accent-color);
    color: white;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    color: var(--primary-color);
}

.product-category {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

.product-actions .btn {
    flex: 1;
    padding: 0.6rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 3rem;
}

.pagination-btn {
    background: none;
    border: 1px solid #ddd;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 0.5rem;
}

.page-number {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid #ddd;
    background: none;
    cursor: pointer;
    transition: var(--transition);
}

.page-number:hover,
.page-number.active {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--primary-color);
}

/* Loading and Error States */
.loading {
    text-align: center;
    padding: 3rem 0;
    color: #666;
}

.loading i {
    margin-right: 0.5rem;
}

.error-message,
.no-products {
    text-align: center;
    padding: 3rem 0;
    color: #666;
}

/* Responsive Design */
@media (max-width: 992px) {
    .products-layout {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        position: static;
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2rem;
    }
    
    .products-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 576px) {
    .filter-actions {
        flex-direction: column;
    }
    
    .pagination {
        flex-direction: column;
        gap: 1rem;
    }
}
