/* Authentication Pages Styles */

.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 2rem;
    background-color: #f8f9fa;
}

.auth-card {
    width: 100%;
    max-width: 500px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: #666;
}

.auth-form .form-group {
    margin-bottom: 1.5rem;
}

.auth-form .form-row {
    display: flex;
    gap: 1rem;
}

.auth-form .form-row .form-group {
    flex: 1;
}

.auth-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.auth-form input[type="text"],
.auth-form input[type="email"],
.auth-form input[type="password"] {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.auth-form input:focus {
    border-color: var(--secondary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(248, 194, 145, 0.2);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
}

.password-strength {
    margin-top: 0.5rem;
}

.strength-meter {
    height: 5px;
    background-color: #eee;
    border-radius: 5px;
    margin-bottom: 0.3rem;
}

.strength-bar {
    height: 100%;
    width: 20%;
    background-color: #ff4d4d;
    border-radius: 5px;
    transition: var(--transition);
}

.strength-text {
    font-size: 0.8rem;
    color: #666;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.remember-me input[type="checkbox"] {
    accent-color: var(--secondary-color);
}

.forgot-password {
    color: var(--accent-color);
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    text-decoration: underline;
}

.auth-button {
    width: 100%;
    padding: 0.8rem;
    background-color: var(--secondary-color);
    color: var(--primary-color);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.auth-button:hover {
    background-color: var(--accent-color);
    color: white;
}

.auth-divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #ddd;
}

.auth-divider span {
    padding: 0 1rem;
    color: #666;
    font-size: 0.9rem;
}

.social-auth {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.social-auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.social-auth-btn:hover {
    background-color: #f5f5f5;
}

.social-auth-btn.google {
    color: #DB4437;
}

.social-auth-btn.facebook {
    color: #4267B2;
}

.auth-footer {
    text-align: center;
    margin-top: 1rem;
    color: #666;
}

.auth-footer a {
    color: var(--accent-color);
    font-weight: 500;
    transition: var(--transition);
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 576px) {
    .auth-card {
        padding: 1.5rem;
    }

    .auth-form .form-row {
        flex-direction: column;
        gap: 0;
    }

    .auth-header h2 {
        font-size: 1.8rem;
    }
}
