<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Detail - 5RRR Store</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/products.css">
    <link rel="stylesheet" href="styles/product-detail.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1><a href="index.html">5RRR</a></h1>
            </div>
            <div class="nav-links">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html" class="active">Shop</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </div>
            <div class="nav-actions">
                <a href="cart.html" class="cart-icon"><i class="fas fa-shopping-cart"></i></a>
                <a href="login.html" class="auth-btn">Login</a>
                <a href="signup.html" class="auth-btn highlight">Sign Up</a>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Breadcrumb -->
        <div class="breadcrumb-container">
            <div class="container">
                <div class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span>/</span>
                    <a href="products.html">Shop</a>
                    <span>/</span>
                    <span class="product-name">Product Name</span>
                </div>
            </div>
        </div>

        <!-- Product Detail Section -->
        <section class="product-detail-section">
            <div class="container">
                <div class="product-detail">
                    <!-- Product details will be loaded dynamically with JavaScript -->
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading product details...
                    </div>
                </div>
            </div>
        </section>

        <!-- Product Tabs -->
        <section class="product-tabs-section">
            <div class="container">
                <div class="product-tabs">
                    <div class="tabs-header">
                        <button class="tab-btn active" data-tab="description">Description</button>
                        <button class="tab-btn" data-tab="details">Additional Information</button>
                        <button class="tab-btn" data-tab="reviews">Reviews (24)</button>
                    </div>
                    <div class="tabs-content">
                        <div class="tab-panel active" id="description">
                            <h3>Product Description</h3>
                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nunc sit amet ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nunc sit amet ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                            <p>Sed euismod, nunc sit amet ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nunc sit amet ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                            <ul>
                                <li>Premium quality materials</li>
                                <li>Comfortable fit</li>
                                <li>Durable construction</li>
                                <li>Stylish design</li>
                            </ul>
                        </div>
                        <div class="tab-panel" id="details">
                            <h3>Additional Information</h3>
                            <table class="details-table">
                                <tr>
                                    <th>Material</th>
                                    <td>100% Cotton</td>
                                </tr>
                                <tr>
                                    <th>Weight</th>
                                    <td>0.3 kg</td>
                                </tr>
                                <tr>
                                    <th>Dimensions</th>
                                    <td>30 × 40 cm</td>
                                </tr>
                                <tr>
                                    <th>Colors</th>
                                    <td>Black, White, Navy</td>
                                </tr>
                                <tr>
                                    <th>Sizes</th>
                                    <td>S, M, L, XL</td>
                                </tr>
                            </table>
                        </div>
                        <div class="tab-panel" id="reviews">
                            <h3>Customer Reviews</h3>
                            <div class="reviews-summary">
                                <div class="average-rating">
                                    <div class="rating-number">4.5</div>
                                    <div class="stars">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                    <div class="total-reviews">Based on 24 reviews</div>
                                </div>
                                <div class="rating-bars">
                                    <div class="rating-bar">
                                        <div class="rating-label">5 stars</div>
                                        <div class="bar-container">
                                            <div class="bar" style="width: 70%"></div>
                                        </div>
                                        <div class="rating-count">18</div>
                                    </div>
                                    <div class="rating-bar">
                                        <div class="rating-label">4 stars</div>
                                        <div class="bar-container">
                                            <div class="bar" style="width: 20%"></div>
                                        </div>
                                        <div class="rating-count">4</div>
                                    </div>
                                    <div class="rating-bar">
                                        <div class="rating-label">3 stars</div>
                                        <div class="bar-container">
                                            <div class="bar" style="width: 5%"></div>
                                        </div>
                                        <div class="rating-count">1</div>
                                    </div>
                                    <div class="rating-bar">
                                        <div class="rating-label">2 stars</div>
                                        <div class="bar-container">
                                            <div class="bar" style="width: 5%"></div>
                                        </div>
                                        <div class="rating-count">1</div>
                                    </div>
                                    <div class="rating-bar">
                                        <div class="rating-label">1 star</div>
                                        <div class="bar-container">
                                            <div class="bar" style="width: 0%"></div>
                                        </div>
                                        <div class="rating-count">0</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="reviews-list">
                                <div class="review-item">
                                    <div class="review-header">
                                        <div class="reviewer-info">
                                            <div class="reviewer-name">John D.</div>
                                            <div class="review-date">June 15, 2023</div>
                                        </div>
                                        <div class="review-rating">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                    </div>
                                    <div class="review-content">
                                        <p>Excellent product! The quality is outstanding and it fits perfectly. I would definitely recommend it to anyone looking for a premium t-shirt.</p>
                                    </div>
                                </div>
                                
                                <div class="review-item">
                                    <div class="review-header">
                                        <div class="reviewer-info">
                                            <div class="reviewer-name">Sarah M.</div>
                                            <div class="review-date">May 28, 2023</div>
                                        </div>
                                        <div class="review-rating">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                    </div>
                                    <div class="review-content">
                                        <p>Great product, comfortable and stylish. The only reason I'm giving it 4 stars instead of 5 is that the color was slightly different than what I expected from the photos.</p>
                                    </div>
                                </div>
                                
                                <div class="review-item">
                                    <div class="review-header">
                                        <div class="reviewer-info">
                                            <div class="reviewer-name">Michael T.</div>
                                            <div class="review-date">May 10, 2023</div>
                                        </div>
                                        <div class="review-rating">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star-half-alt"></i>
                                        </div>
                                    </div>
                                    <div class="review-content">
                                        <p>Very satisfied with my purchase. The material is high quality and the design is exactly as shown. Shipping was fast too!</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="write-review">
                                <h4>Write a Review</h4>
                                <form class="review-form">
                                    <div class="form-group">
                                        <label for="review-rating">Your Rating</label>
                                        <div class="rating-input">
                                            <i class="far fa-star" data-rating="1"></i>
                                            <i class="far fa-star" data-rating="2"></i>
                                            <i class="far fa-star" data-rating="3"></i>
                                            <i class="far fa-star" data-rating="4"></i>
                                            <i class="far fa-star" data-rating="5"></i>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="review-text">Your Review</label>
                                        <textarea id="review-text" rows="5" required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Submit Review</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Related Products -->
        <section class="related-products">
            <div class="container">
                <h2 class="section-title">You May Also Like</h2>
                <div class="product-grid">
                    <!-- Related products will be loaded dynamically with JavaScript -->
                </div>
            </div>
        </section>
        
        <!-- Newsletter Section -->
        <section class="newsletter">
            <div class="container">
                <div class="newsletter-content">
                    <h2>Join Our Newsletter</h2>
                    <p>Subscribe to get special offers, free giveaways, and once-in-a-lifetime deals.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-primary">Subscribe</button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>5RRR Store</h3>
                <p>Premium clothing and essentials for the modern lifestyle.</p>
                <div class="social-icons">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-pinterest"></i></a>
                </div>
            </div>
            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Shop</a></li>
                    <li><a href="about.html">About Us</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Customer Service</h3>
                <ul>
                    <li><a href="faq.html">FAQ</a></li>
                    <li><a href="shipping.html">Shipping & Returns</a></li>
                    <li><a href="privacy.html">Privacy Policy</a></li>
                    <li><a href="terms.html">Terms & Conditions</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Contact Us</h3>
                <p><i class="fas fa-map-marker-alt"></i> 123 Fashion St, Style City</p>
                <p><i class="fas fa-phone"></i> +****************</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 5RRR Store. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/products.js" type="module"></script>
    <script>
        // Product tabs functionality
        document.addEventListener('DOMContentLoaded', () => {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabPanels = document.querySelectorAll('.tab-panel');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons and panels
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanels.forEach(panel => panel.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding panel
                    button.classList.add('active');
                    const tabId = button.dataset.tab;
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // Rating input functionality
            const ratingStars = document.querySelectorAll('.rating-input i');
            let selectedRating = 0;
            
            ratingStars.forEach(star => {
                star.addEventListener('mouseover', () => {
                    const rating = parseInt(star.dataset.rating);
                    highlightStars(rating);
                });
                
                star.addEventListener('mouseout', () => {
                    highlightStars(selectedRating);
                });
                
                star.addEventListener('click', () => {
                    selectedRating = parseInt(star.dataset.rating);
                    highlightStars(selectedRating);
                });
            });
            
            function highlightStars(rating) {
                ratingStars.forEach(star => {
                    const starRating = parseInt(star.dataset.rating);
                    if (starRating <= rating) {
                        star.classList.remove('far');
                        star.classList.add('fas');
                    } else {
                        star.classList.remove('fas');
                        star.classList.add('far');
                    }
                });
            }
        });
    </script>
</body>
</html>
