{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch 5RRR Store",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:3308",
            "webRoot": "${workspaceFolder}",
            "preLaunchTask": "Start 5RRR Store Server",
            "serverReadyAction": {
                "pattern": "server running at (https?://\\S+|[0-9]+)",
                "uriFormat": "http://localhost:3308",
                "action": "openExternally"
            }
        },
        {
            "name": "Launch with HTTP Server",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:3308",
            "webRoot": "${workspaceFolder}",
            "preLaunchTask": "Start HTTP Server (Alternative)"
        },
        {
            "name": "Attach to Chrome",
            "type": "chrome",
            "request": "attach",
            "port": 9222,
            "webRoot": "${workspaceFolder}"
        }
    ]
}