// Authentication functionality for 5RRR Store
import supabase from './supabase.js';

// DOM Elements
document.addEventListener('DOMContentLoaded', () => {
    // Login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Signup form
    const signupForm = document.getElementById('signup-form');
    if (signupForm) {
        signupForm.addEventListener('submit', handleSignup);
        
        // Password strength meter
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.addEventListener('input', updatePasswordStrength);
        }
    }

    // Toggle password visibility
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', togglePasswordVisibility);
    });

    // Check if user is already logged in
    checkAuthState();
});

/**
 * Handle login form submission
 * @param {Event} e - Form submit event
 */
async function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('remember')?.checked || false;
    
    try {
        showLoading(true);
        
        const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
        });
        
        if (error) throw error;
        
        // Save session if remember me is checked
        if (rememberMe) {
            localStorage.setItem('supabase.auth.token', JSON.stringify(data.session));
        }
        
        showMessage('Login successful! Redirecting...', 'success');
        
        // Redirect to home page after successful login
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
        
    } catch (error) {
        console.error('Login error:', error);
        showMessage(error.message || 'Failed to login. Please try again.', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Handle signup form submission
 * @param {Event} e - Form submit event
 */
async function handleSignup(e) {
    e.preventDefault();
    
    const firstName = document.getElementById('first-name').value;
    const lastName = document.getElementById('last-name').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    
    // Validate passwords match
    if (password !== confirmPassword) {
        showMessage('Passwords do not match', 'error');
        return;
    }
    
    try {
        showLoading(true);
        
        // Create user in Supabase Auth
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email,
            password,
            options: {
                data: {
                    first_name: firstName,
                    last_name: lastName
                }
            }
        });
        
        if (authError) throw authError;
        
        // Store additional user data in profiles table
        const { error: profileError } = await supabase
            .from('profiles')
            .insert([
                { 
                    id: authData.user.id,
                    first_name: firstName,
                    last_name: lastName,
                    email: email
                }
            ]);
        
        if (profileError) throw profileError;
        
        showMessage('Account created successfully! Please check your email to confirm your account.', 'success');
        
        // Redirect to login page after successful signup
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 3000);
        
    } catch (error) {
        console.error('Signup error:', error);
        showMessage(error.message || 'Failed to create account. Please try again.', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Toggle password visibility
 * @param {Event} e - Click event
 */
function togglePasswordVisibility(e) {
    const button = e.target.closest('.toggle-password');
    const passwordInput = button.previousElementSibling;
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        button.classList.remove('fa-eye');
        button.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        button.classList.remove('fa-eye-slash');
        button.classList.add('fa-eye');
    }
}

/**
 * Update password strength meter
 * @param {Event} e - Input event
 */
function updatePasswordStrength(e) {
    const password = e.target.value;
    const strengthBar = document.querySelector('.strength-bar');
    const strengthText = document.querySelector('.strength-text');
    
    if (!strengthBar || !strengthText) return;
    
    // Calculate password strength
    let strength = 0;
    let feedback = '';
    
    if (password.length >= 8) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    
    // Update strength bar width and color
    strengthBar.style.width = `${strength}%`;
    
    if (strength <= 25) {
        strengthBar.style.backgroundColor = '#ff4d4d';
        feedback = 'Weak';
    } else if (strength <= 50) {
        strengthBar.style.backgroundColor = '#ffa64d';
        feedback = 'Fair';
    } else if (strength <= 75) {
        strengthBar.style.backgroundColor = '#ffff4d';
        feedback = 'Good';
    } else {
        strengthBar.style.backgroundColor = '#4dff4d';
        feedback = 'Strong';
    }
    
    strengthText.textContent = `Password strength: ${feedback}`;
}

/**
 * Check if user is already logged in
 */
async function checkAuthState() {
    try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) throw error;
        
        if (session) {
            // User is logged in
            updateUIForLoggedInUser(session.user);
        }
    } catch (error) {
        console.error('Auth state check error:', error);
    }
}

/**
 * Update UI for logged in user
 * @param {Object} user - User object
 */
function updateUIForLoggedInUser(user) {
    const authBtns = document.querySelectorAll('.auth-btn');
    const navActions = document.querySelector('.nav-actions');
    
    if (!navActions) return;
    
    // Remove login/signup buttons
    authBtns.forEach(btn => btn.remove());
    
    // Add user menu
    const userMenu = document.createElement('div');
    userMenu.className = 'user-menu';
    userMenu.innerHTML = `
        <button class="user-menu-btn">
            <i class="fas fa-user-circle"></i>
            <span>My Account</span>
        </button>
        <div class="user-dropdown">
            <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
            <a href="orders.html"><i class="fas fa-shopping-bag"></i> Orders</a>
            <a href="wishlist.html"><i class="fas fa-heart"></i> Wishlist</a>
            <button id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</button>
        </div>
    `;
    
    // Insert before cart icon
    const cartIcon = navActions.querySelector('.cart-icon');
    navActions.insertBefore(userMenu, cartIcon);
    
    // Add logout functionality
    document.getElementById('logout-btn').addEventListener('click', handleLogout);
    
    // Toggle user dropdown
    const userMenuBtn = document.querySelector('.user-menu-btn');
    userMenuBtn.addEventListener('click', () => {
        document.querySelector('.user-dropdown').classList.toggle('active');
    });
}

/**
 * Handle user logout
 */
async function handleLogout() {
    try {
        showLoading(true);
        
        const { error } = await supabase.auth.signOut();
        
        if (error) throw error;
        
        // Clear any stored tokens
        localStorage.removeItem('supabase.auth.token');
        
        showMessage('Logged out successfully!', 'success');
        
        // Reload page after logout
        setTimeout(() => {
            window.location.reload();
        }, 1500);
        
    } catch (error) {
        console.error('Logout error:', error);
        showMessage(error.message || 'Failed to logout. Please try again.', 'error');
    } finally {
        showLoading(false);
    }
}

/**
 * Show loading state
 * @param {boolean} isLoading - Whether loading is active
 */
function showLoading(isLoading) {
    const authButton = document.querySelector('.auth-button');
    
    if (!authButton) return;
    
    if (isLoading) {
        authButton.disabled = true;
        authButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    } else {
        authButton.disabled = false;
        authButton.innerHTML = authButton.closest('#login-form') ? 'Login' : 'Create Account';
    }
}

/**
 * Show message to user
 * @param {string} message - Message to display
 * @param {string} type - Message type (success, error)
 */
function showMessage(message, type) {
    // Check if message container exists, if not create it
    let messageContainer = document.querySelector('.message-container');
    
    if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.className = 'message-container';
        document.body.appendChild(messageContainer);
    }
    
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    messageElement.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add to container
    messageContainer.appendChild(messageElement);
    
    // Remove after delay
    setTimeout(() => {
        messageElement.classList.add('fade-out');
        setTimeout(() => {
            messageElement.remove();
        }, 500);
    }, 3000);
}
