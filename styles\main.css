/* Main CSS for 5RRR Store */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3a3a3a;
    --secondary-color: #f8c291;
    --accent-color: #e55039;
    --light-color: #f5f5f5;
    --dark-color: #222222;
    --text-color: #333333;
    --border-radius: 4px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.section-title {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: 600;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 0.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: var(--secondary-color);
}

/* Layout */
.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

section {
    padding: 4rem 2rem;
}

/* Navigation */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: white;
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    letter-spacing: 1px;
}

.nav-links ul {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-icon {
    font-size: 1.2rem;
    transition: var(--transition);
}

.cart-icon:hover {
    color: var(--accent-color);
}

.auth-btn {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: 1px solid var(--primary-color);
}

.auth-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.auth-btn.highlight {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--primary-color);
}

.auth-btn.highlight:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
}

.menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80vh;
    background: linear-gradient(to right, #fff, #f8f9fa);
    padding: 0 5%;
}

.hero-content {
    flex: 1;
    max-width: 500px;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #666;
}

.cta-button {
    display: inline-block;
    padding: 0.8rem 2rem;
    background-color: var(--secondary-color);
    color: var(--primary-color);
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.cta-button:hover {
    background-color: var(--accent-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

.hero-image img {
    max-width: 90%;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
}

/* Categories Section */
.categories {
    background-color: white;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.category-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.category-card img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.category-card h3 {
    position: absolute;
    bottom: 60px;
    left: 0;
    width: 100%;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 0.9);
    text-align: center;
    font-weight: 600;
}

.category-link {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1rem;
    background-color: var(--secondary-color);
    color: var(--primary-color);
    text-align: center;
    font-weight: 500;
    transition: var(--transition);
}

.category-link:hover {
    background-color: var(--accent-color);
    color: white;
}

/* Featured Products */
.featured-products {
    background-color: #f8f9fa;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.view-all-btn {
    display: block;
    width: max-content;
    margin: 0 auto;
    padding: 0.8rem 2rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.view-all-btn:hover {
    background-color: var(--accent-color);
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 3rem 2rem;
    }

    .hero-content {
        margin-bottom: 2rem;
    }

    .hero-image {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .nav-links, .nav-actions .auth-btn {
        display: none;
    }

    .menu-toggle {
        display: block;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }
}

/* Footer Styles */
footer {
    background-color: var(--primary-color);
    color: white;
    padding: 3rem 2rem 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--secondary-color);
}

.footer-section p {
    margin-bottom: 0.5rem;
}

.social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icons a {
    display: inline-block;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--secondary-color);
    padding-left: 5px;
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}
