// Main JavaScript for 5RRR Store

document.addEventListener('DOMContentLoaded', () => {
    // Initialize mobile menu
    initMobileMenu();
    
    // Add CSS class for animations
    document.body.classList.add('loaded');
    
    // Initialize newsletter form
    initNewsletterForm();
});

/**
 * Initialize mobile menu functionality
 */
function initMobileMenu() {
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    const navActions = document.querySelector('.nav-actions');
    
    if (!menuToggle || !navLinks) return;
    
    // Create mobile menu container if it doesn't exist
    let mobileMenu = document.querySelector('.mobile-menu');
    
    if (!mobileMenu) {
        mobileMenu = document.createElement('div');
        mobileMenu.className = 'mobile-menu';
        
        // Clone navigation links
        const navLinksClone = navLinks.cloneNode(true);
        
        // Clone auth buttons
        const authButtons = navActions.querySelectorAll('.auth-btn');
        const authButtonsContainer = document.createElement('div');
        authButtonsContainer.className = 'mobile-auth-buttons';
        
        authButtons.forEach(button => {
            const buttonClone = button.cloneNode(true);
            authButtonsContainer.appendChild(buttonClone);
        });
        
        // Add to mobile menu
        mobileMenu.appendChild(navLinksClone);
        mobileMenu.appendChild(authButtonsContainer);
        
        // Add close button
        const closeButton = document.createElement('button');
        closeButton.className = 'close-menu';
        closeButton.innerHTML = '<i class="fas fa-times"></i>';
        mobileMenu.appendChild(closeButton);
        
        // Add to DOM
        document.body.appendChild(mobileMenu);
        
        // Add event listener to close button
        closeButton.addEventListener('click', () => {
            mobileMenu.classList.remove('active');
            document.body.classList.remove('menu-open');
        });
    }
    
    // Toggle mobile menu
    menuToggle.addEventListener('click', () => {
        mobileMenu.classList.toggle('active');
        document.body.classList.toggle('menu-open');
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
        if (
            mobileMenu.classList.contains('active') && 
            !mobileMenu.contains(e.target) && 
            !menuToggle.contains(e.target)
        ) {
            mobileMenu.classList.remove('active');
            document.body.classList.remove('menu-open');
        }
    });
}

/**
 * Initialize newsletter form
 */
function initNewsletterForm() {
    const newsletterForm = document.querySelector('.newsletter-form');
    
    if (!newsletterForm) return;
    
    newsletterForm.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const emailInput = newsletterForm.querySelector('input[type="email"]');
        const email = emailInput.value.trim();
        
        if (!email) {
            showMessage('Please enter your email address.', 'error');
            return;
        }
        
        // In the future, this will send to backend/Supabase
        // For now, just show success message
        showMessage('Thank you for subscribing to our newsletter!', 'success');
        
        // Reset form
        newsletterForm.reset();
    });
}

/**
 * Show message to user
 * @param {string} message - Message to display
 * @param {string} type - Message type (success, error)
 */
function showMessage(message, type) {
    // Check if message container exists, if not create it
    let messageContainer = document.querySelector('.message-container');
    
    if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.className = 'message-container';
        document.body.appendChild(messageContainer);
    }
    
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    messageElement.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add to container
    messageContainer.appendChild(messageElement);
    
    // Remove after delay
    setTimeout(() => {
        messageElement.classList.add('fade-out');
        setTimeout(() => {
            messageElement.remove();
        }, 500);
    }, 3000);
}

/**
 * Add CSS for messages (since it's not in the CSS file yet)
 */
(function addMessageStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .message-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 300px;
        }
        
        .message {
            background-color: white;
            border-radius: 4px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            animation: slideIn 0.3s ease;
        }
        
        .message.success {
            border-left: 4px solid #4CAF50;
        }
        
        .message.success i {
            color: #4CAF50;
        }
        
        .message.error {
            border-left: 4px solid #F44336;
        }
        
        .message.error i {
            color: #F44336;
        }
        
        .message.fade-out {
            animation: fadeOut 0.5s ease forwards;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes fadeOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .mobile-menu {
            position: fixed;
            top: 0;
            right: -300px;
            width: 280px;
            height: 100vh;
            background-color: white;
            z-index: 1000;
            padding: 2rem;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            overflow-y: auto;
        }
        
        .mobile-menu.active {
            right: 0;
        }
        
        .mobile-menu ul {
            flex-direction: column;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .mobile-menu .mobile-auth-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .mobile-menu .auth-btn {
            text-align: center;
        }
        
        .close-menu {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--primary-color);
        }
        
        body.menu-open {
            overflow: hidden;
        }
        
        /* Animation classes */
        .loaded .hero-content h1,
        .loaded .hero-content p,
        .loaded .hero-content .cta-button {
            animation: fadeInUp 0.8s ease forwards;
            opacity: 0;
        }
        
        .loaded .hero-content p {
            animation-delay: 0.2s;
        }
        
        .loaded .hero-content .cta-button {
            animation-delay: 0.4s;
        }
        
        .loaded .hero-image img {
            animation: fadeIn 1s ease forwards;
            opacity: 0;
            animation-delay: 0.3s;
        }
        
        .loaded .category-card {
            animation: fadeInUp 0.6s ease forwards;
            opacity: 0;
        }
        
        .loaded .category-card:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .loaded .category-card:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        .loaded .category-card:nth-child(4) {
            animation-delay: 0.6s;
        }
        
        @keyframes fadeInUp {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        /* Cart count badge */
        .cart-icon {
            position: relative;
        }
        
        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--accent-color);
            color: white;
            font-size: 0.7rem;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
    `;
    document.head.appendChild(style);
})();
