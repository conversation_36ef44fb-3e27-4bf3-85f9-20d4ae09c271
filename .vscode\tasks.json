{"version": "2.0.0", "tasks": [{"label": "Start 5RRR Store Server", "type": "shell", "command": "node", "args": ["server.js"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "runOptions": {"runOn": "folderOpen"}, "problemMatcher": []}, {"label": "Start HTTP Server (Alternative)", "type": "shell", "command": "npx", "args": ["http-server", ".", "-p", "3308", "-o", "-c-1"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}]}