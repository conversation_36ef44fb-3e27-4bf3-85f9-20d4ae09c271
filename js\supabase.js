// Supabase client configuration
import { createClient } from 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.38.4/+esm'

// Supabase URL and anon key
// IMPORTANT: Replace these with your actual Supabase URL and anon key
// You can find these in your Supabase project settings > API
const supabaseUrl = 'YOUR_SUPABASE_URL'
const supabaseKey = 'YOUR_SUPABASE_ANON_KEY'

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey)

/**
 * Database Schema:
 *
 * 1. profiles - User profiles
 *    - id (uuid, primary key, references auth.users.id)
 *    - first_name (text)
 *    - last_name (text)
 *    - email (text)
 *    - avatar_url (text, nullable)
 *    - created_at (timestamp with time zone, default: now())
 *
 * 2. products - Product information
 *    - id (uuid, primary key)
 *    - name (text)
 *    - description (text)
 *    - price (numeric)
 *    - category (text)
 *    - image (text)
 *    - featured (boolean, default: false)
 *    - new (boolean, default: false)
 *    - created_at (timestamp with time zone, default: now())
 *
 * 3. product_variants - Product variants (colors, sizes)
 *    - id (uuid, primary key)
 *    - product_id (uuid, references products.id)
 *    - color (text, nullable)
 *    - size (text, nullable)
 *    - stock (integer, default: 0)
 *
 * 4. orders - Customer orders
 *    - id (uuid, primary key)
 *    - user_id (uuid, references auth.users.id)
 *    - status (text, default: 'pending')
 *    - total (numeric)
 *    - created_at (timestamp with time zone, default: now())
 *
 * 5. order_items - Items in an order
 *    - id (uuid, primary key)
 *    - order_id (uuid, references orders.id)
 *    - product_id (uuid, references products.id)
 *    - variant_id (uuid, references product_variants.id, nullable)
 *    - quantity (integer)
 *    - price (numeric)
 *
 * 6. reviews - Product reviews
 *    - id (uuid, primary key)
 *    - product_id (uuid, references products.id)
 *    - user_id (uuid, references auth.users.id)
 *    - rating (integer)
 *    - comment (text)
 *    - created_at (timestamp with time zone, default: now())
 */

export default supabase
